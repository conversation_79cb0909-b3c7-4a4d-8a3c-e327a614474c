import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getKVKeyBlog, getKVKeyBlogHeads } from "@/lib/utils";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { z } from "zod";
import { blogPostSchema, blogPostTranslationSchema } from "@/server/db/schema-blog.server";

export const blogItemSchema = z.object({
	blogId: z.number().int().min(1, "Blog ID is required"),
	blogItemId: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	metaTitle: z.string().optional(),
	metaDescription: z.string().optional(),
	image: z.string().nullable(),
	intro: z.string().trim().nonempty({
		message: "Summary(Intro) is required",
	}),
	html: z.string().trim().nonempty({
		message: "Blog content is required",
	}),
	publishedAt: z.string().transform((val) => {
		const date = new Date(val);
		if (isNaN(date.getTime())) {
			throw new Error("Invalid date format");
		}
		return date;
	}),
	status: z.number().int(),
});

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const body = await req.json();
		if (process.env.NODE_ENV === "development") {
			console.log("blog-item body:", body);
		}

		const validatedData = blogItemSchema.parse(body);
		const db = getDB();

		// Check if the parent changelog exists
		const parentBlog = await db.query.blogPostSchema.findFirst({
			where: eq(blogPostSchema.id, validatedData.blogId),
		});
		if (!parentBlog) {
			return NextResponse.json({
				status: 404,
				message: "Parent blog not found.",
			});
		}

		if (validatedData.blogItemId) {
			// Update existing changelog translation
			const updatedTranslation = await db
				.update(blogPostTranslationSchema)
				.set({
					lang: validatedData.lang,
					status: validatedData.status,
					title: validatedData.title,
					metaTitle: validatedData.metaTitle,
					metaDescription: validatedData.metaDescription,
					image: validatedData.image,
					intro: validatedData.intro,
					html: validatedData.html,
					publishedAt: validatedData.publishedAt,
				})
				.where(eq(blogPostTranslationSchema.id, validatedData.blogItemId))
				.returning({ id: blogPostTranslationSchema.id });

			// Clear cache for this language
			await deleteValue(getKVKeyBlogHeads(validatedData.lang, 1));
			await deleteValue(getKVKeyBlog(validatedData.lang, parentBlog.slug));

			return NextResponse.json({
				status: 200,
				message: "Blog translation updated successfully",
				changelogItemId: updatedTranslation[0].id,
			});
		} else {
			// Create new changelog translation
			const newTranslation = await db
				.insert(blogPostTranslationSchema)
				.values({
					postId: validatedData.blogId,
					lang: validatedData.lang,
					status: validatedData.status,
					title: validatedData.title,
					metaTitle: validatedData.metaTitle,
					metaDescription: validatedData.metaDescription,
					image: validatedData.image,
					intro: validatedData.intro,
					html: validatedData.html,
					publishedAt: validatedData.publishedAt,
				})
				.onConflictDoNothing({
					target: [blogPostTranslationSchema.postId, blogPostTranslationSchema.lang],
				})
				.returning({ id: blogPostTranslationSchema.id });

			if (newTranslation.length === 0) {
				return NextResponse.json({
					status: 400,
					message: "Blog translation already exists.",
				});
			}

			// Clear cache for this language
			await deleteValue(getKVKeyBlogHeads(validatedData.lang, 1));

			return NextResponse.json({
				status: 200,
				message: "Blog translation created successfully",
				changelogItemId: newTranslation[0].id,
			});
		}
	} catch (error) {
		console.error("Error processing blog item request:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json({
				status: 400,
				message: "Invalid request data",
				errors: error.errors,
			});
		}

		return NextResponse.json({
			status: 500,
			message: "Internal server error",
		});
	}
}

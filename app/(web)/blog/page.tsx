import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import BlogItems from "./blog-items";
import { getBlogHeadsWithCategory } from "@/server/utils-blog.server";

export const metadata: Metadata = {
	title: `${WEBNAME} Blog: Get Better at Portrait Editing & Photo Styling`,
	description: "",
	alternates: {
		canonical: "/blog",
	},
};

export default async function Page() {
	const { blogHeads, blogCategories } = await getBlogHeadsWithCategory("en", 1);
	return (
		<main className="min-h-screen pb-20">
			<div className="container pt-14 pb-14 md:max-w-4xl">
				<div className="flex flex-col gap-2">
					<h1 className="text-primary mt-8 text-base font-medium text-pretty">{WEBNAME} Blog</h1>
					<p className="text-primary text-4xl font-semibold text-balance">News, insights and more</p>
				</div>
			</div>

			<div className="container pb-20 md:max-w-4xl">
				<BlogItems blogHeads={blogHeads} blogCategories={blogCategories} />
			</div>
		</main>
	);
}
